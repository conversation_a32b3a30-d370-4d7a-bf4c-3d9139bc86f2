import { datadogLogs } from "@datadog/browser-logs";
import { APIrequest } from "../API/axios";
import { Endpoints } from "../API/Endpoints/routes";
import { logger } from "../../../../utils";
// import { apiLogger } from "../utils/helper";

export const SettingsMenuServices = {
  startCloudRecording: async (meetingId, user , token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.recording_start.method,
        endpoint: Endpoints.recording_start.url,
        payload: {
          meeting_uid: meetingId,
        },
        token
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.recording_start.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.recording_start.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  stopCloudRecording: async (meetingId, egressId, user, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.recording_stop.method,
        endpoint: Endpoints.recording_stop.url,
        payload: {
          meeting_uid: meetingId,
          egress_id: egressId,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error stopping cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.recording_stop.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success stopping cloud recording", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.recording_stop.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  reportAProblem: async (
    id,
    preferredVideoServerId,
    userDescription,
    audioIssue,
    videoIssue,
    captionIssue,
    user,
    token = null
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.report_problem.method,
        endpoint: Endpoints.report_problem.url,
        payload: {
          meeting_uid: id,
          preferred_video_server_id: preferredVideoServerId,
          user_description: userDescription,
          audio_issue: audioIssue,
          video_issue: videoIssue,
          caption_issue: captionIssue,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error reporting a problem", {
          response,
          payload: {
            meeting_uid: id,
            preferred_video_server_id: preferredVideoServerId,
            user_description: userDescription,
            audio_issue: audioIssue,
            video_issue: videoIssue,
            caption_issue: captionIssue,
          },
          endpoint: Endpoints.report_problem.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success reporting a problem", {
          response,
          payload: {
            meeting_uid: id,
            preferred_video_server_id: preferredVideoServerId,
            user_description: userDescription,
            audio_issue: audioIssue,
            video_issue: videoIssue,
            caption_issue: captionIssue,
          },
          endpoint: Endpoints.report_problem.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  livestreamStart: async (meetingId, url, streamKey, user, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.livestream_start.method,
        endpoint: Endpoints.livestream_start.url,
        payload: {
          meeting_uid: meetingId,
          stream_rtmp_urls: [`${url}/${streamKey}`],
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            // stream_rtmp_urls: [`${url}/${streamKey}`],
          },
          endpoint: Endpoints.livestream_start.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            // stream_rtmp_urls: [`${url}/${streamKey}`],
          },
          endpoint: Endpoints.livestream_start.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  livestreamStop: async (meetingId, egressId, user) => {
    try {
      const response = await APIrequest({
        method: Endpoints.livestream_stop.method,
        endpoint: Endpoints.livestream_stop.url,
        payload: {
          meeting_uid: meetingId,
          egress_id: egressId,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error stopping livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.livestream_stop.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success stopping livestream", {
          response,
          payload: {
            meeting_uid: meetingId,
            egress_id: egressId,
          },
          endpoint: Endpoints.livestream_stop.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  startLiveTranscription: async (meetingId, user, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.start_live_transcription.method,
        endpoint: Endpoints.start_live_transcription.url,
        payload: {
          meeting_uid: meetingId,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error starting live transcription", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.start_live_transcription.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success starting live transcription", {
          response,
          payload: {
            meeting_uid: meetingId,
          },
          endpoint: Endpoints.start_live_transcription.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  setLiveTranscriptionDetail: async (
    meetingId,
    languageCode,
    language,
    isEnabled,
    user,
    token = null
  ) => {
    try {
      const response = await APIrequest({
        method: Endpoints.setTranscriptionDetail.method,
        endpoint: Endpoints.setTranscriptionDetail.url,
        payload: {
          meeting_uid: meetingId,
          transcription_enable: isEnabled,
          transcription_lang_iso: languageCode,
          transcription_lang_title: language,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error setting live transcription detail", {
          response,
          payload: {
            meeting_uid: meetingId,
            transcription_enable: isEnabled,
            transcription_lang_iso: languageCode,
            transcription_lang_title: language,
          },
          endpoint: Endpoints.setTranscriptionDetail.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success setting live transcription detail", {
          response,
          payload: {
            meeting_uid: meetingId,
            transcription_enable: isEnabled,
            transcription_lang_iso: languageCode,
            transcription_lang_title: language,
          },
          endpoint: Endpoints.setTranscriptionDetail.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  uploadVirtualBackground: async (file, user) => {
    try {
      const response = await APIrequest({
        method: Endpoints.set_virtual_background.method,
        endpoint: Endpoints.set_virtual_background.url,
        payload: file,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error uploading virtual background", {
          response,
          payload: file,
          endpoint: Endpoints.set_virtual_background.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success uploading virtual background", {
          response,
          payload: file,
          endpoint: Endpoints.set_virtual_background.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getVirtualBackground: async (user, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.get_virtual_background.method,
        endpoint: Endpoints.get_virtual_background.url,
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting virtual background", {
          response,
          endpoint: Endpoints.get_virtual_background.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success getting virtual background", {
          response,
          endpoint: Endpoints.get_virtual_background.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  deleteVirtualBackground: async (id, user) => {
    try {
      const response = await APIrequest({
        method: Endpoints.delete_virtual_background.method,
        endpoint: Endpoints.delete_virtual_background.url,
        payload: {
          id,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error deleting virtual background", {
          response,
          payload: {
            id,
          },
          endpoint: Endpoints.delete_virtual_background.url,
          user,
        });
      } else {
        datadogLogs.logger.info("Success deleting virtual background", {
          response,
          payload: {
            id,
          },
          endpoint: Endpoints.delete_virtual_background.url,
          user,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getLiveTranscriptionDetail: async (langId, textContent) => {
    try {
      const response = await APIrequest({
        method: Endpoints.getTranscriptionDetail.method,
        endpoint: Endpoints.getTranscriptionDetail.url,
        payload: {
          target_language: langId,
          text: textContent,
        },
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting live transcription detail", {
          response,
          payload: {
            target_language: langId,
            text: textContent,
          },
          endpoint: Endpoints.getTranscriptionDetail.url,
        });
      } else {
        datadogLogs.logger.info("Success getting live transcription detail", {
          response,
          payload: {
            target_language: langId,
            text: textContent,
          },
          endpoint: Endpoints.getTranscriptionDetail.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  getTextTranslationDetail: async (payload, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.meetingTranslateText.method,
        endpoint: Endpoints.meetingTranslateText.url,
        payload: { ...payload },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error getting live transcription detail", {
          response,
          payload: { ...payload },
          endpoint: Endpoints.getTranscriptionDetail.url,
        });
      } else {
        datadogLogs.logger.info("Success getting live transcription detail", {
          response,
          payload: { ...payload },
          endpoint: Endpoints.getTranscriptionDetail.url,
        });
      }

      return response;
    } catch (error) {
      throw new Error(error);
    }
  },
  // agents services
  dispatchAgentService: async (meetingUid, agentId, agentName, token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.dispatchAgent().method,
        endpoint: Endpoints.dispatchAgent().url,
        payload: {
          meeting_uid: meetingUid,
          agent_id: agentId,
          agent_name: agentName,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error dispatching agent", {
          response,
          payload: {
            meeting_uid: meetingUid,
            agent_id: agentId,
            agent_name: agentName,
          },
          endpoint: Endpoints.dispatchAgent().url,
        });
      } else {
        datadogLogs.logger.info("Success dispatching agent", {
          response,
          payload: {
            meeting_uid: meetingUid,
            agent_id: agentId,
            agent_name: agentName,
          },
          endpoint: Endpoints.dispatchAgent().url,
        });
      }

      return response;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  deDispatchAgentService: async (meetingUid, dispatchId , token = null) => {
    try {
      const response = await APIrequest({
        method: Endpoints.deDispatchAgent().method,
        endpoint: Endpoints.deDispatchAgent().url,
        payload: {
          meeting_uid: meetingUid,
          dispatch_id: dispatchId,
        },
        token,
      });

      if (response.success === 0) {
        datadogLogs.logger.error("Error de-dispatching agent", {
          response,
          payload: {
            meeting_uid: meetingUid,
            dispatch_id: dispatchId,
          },
          endpoint: Endpoints.deDispatchAgent().url,
        });
      } else {
        datadogLogs.logger.info("Success de-dispatching agent", {
          response,
          payload: {
            meeting_uid: meetingUid,
            dispatch_id: dispatchId,
          },
          endpoint: Endpoints.deDispatchAgent().url,
        });
      }

      return response;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  
};
