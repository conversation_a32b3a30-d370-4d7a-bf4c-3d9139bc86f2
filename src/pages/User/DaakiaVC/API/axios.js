import axios from "axios";
import momentTimezone from "moment-timezone";
import { datadogLogs } from "@datadog/browser-logs";
import isElectron from "is-electron";
import { getLocalStorageToken } from "../utils/helper";
import { constants } from "../utils/constants";

export const APIrequest = async ({
  method,
  endpoint,
  payload,
  token = null,
  needLogHeader = false,
  formHeaders = null,
}) => {
  let apiToken = token !== null ? token : getLocalStorageToken();
  const isElectronApp = isElectron();

  const axiosConfig = {
    method: method || "GET",
    baseURL: constants.STAG_BASE_URL,
    headers: {
      "content-type": "application/json",
      "X-Frame-Options": "sameorigin",
      timezone: momentTimezone.tz.guess(true),
      platform: isElectronApp ? "desktop" : "web",
    },
  };

  if (formHeaders) {
    axiosConfig.headers = { ...axiosConfig.headers, ...formHeaders };
  }

  if (apiToken) {
    axiosConfig.headers.Authorization = apiToken;
  }
  if (endpoint) {
    axiosConfig.url = endpoint;
  }

  if (payload instanceof FormData) {
    axiosConfig.headers["Content-Type"] = "multipart/form-data";
    axiosConfig.data = payload;
  } else if (payload) {
    const bodyPayload = {};
    for (const key in payload) {
      if (Object.hasOwnProperty.call(payload, key)) {
        let element = payload[key];
        if (typeof element === "string") {
          element = element.trim();
        }
        if (![null, undefined, NaN].includes(element)) {
          bodyPayload[key] = element;
        }
      }
    }
    axiosConfig.data = bodyPayload;
  }

  if (needLogHeader) {
    datadogLogs.logger.info("Header data", {
      token: apiToken,
      inHeader: axiosConfig.headers,
      URL: axiosConfig.baseURL + endpoint
    });
  }

  try {
    const response = await axios(axiosConfig);
    return response.data;
  } catch (error) {
    if (error.response) {
      // Axios errors for 4xx, 5xx
      throw new Error(`${JSON.stringify(error.response.data?.message)}`);
    } else {
      // Network error or other issues
      throw new Error(error.message || "An unknown error occurred");
    }
  }
};
